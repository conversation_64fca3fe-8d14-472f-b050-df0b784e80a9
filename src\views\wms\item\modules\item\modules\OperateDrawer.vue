<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { TreeOption } from 'naive-ui';
import { itemApi } from '@/service/api/wms';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { suggestCode } from '@/utils/common';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Wms.Item | null;
  /** the meta options */
  metaOptions?: TreeOption[];
  /** the meta id */
  metaId?: number | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增物料',
    edit: '编辑物料'
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.Wms.Item,
  | 'name'
  | 'code'
  | 'summary'
  | 'order'
  | 'status'
  | 'metaId'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    code: '',
    summary: '',
    order: 0,
    status: true,
    metaId: props.metaId || 0
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'code' | 'metaId'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  code: defaultRequiredRule,
  metaId: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } = props.operateType === 'edit' ? await itemApi.save(model.value) : await itemApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="分类" path="metaId">
          <NTreeSelect
            v-model:value="model.metaId"
            :options="metaOptions"
            key-field="id"
            label-field="name"
            placeholder="请选择分类"
            clearable
          />
        </NFormItem>
        <NFormItem label="名称" path="name">
          <NInput v-model:value="model.name" placeholder="请输入名称" clearable />
        </NFormItem>
        <NFormItem label="编码" path="code">
          <NInputGroup>
            <NInput v-model:value="model.code" placeholder="请输入编码" clearable />
            <NButton @click="model.code = suggestCode('WL')">建议</NButton>
          </NInputGroup>
        </NFormItem>
        <NFormItem label="备注" path="summary">
          <NInput v-model:value="model.summary" placeholder="请输入备注" type="textarea" clearable />
        </NFormItem>
        <NFormItem label="排序" path="order">
          <NInputNumber v-model:value="model.order" placeholder="请输入排序" clearable />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSwitch v-model:value="model.status" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
